import pygame
import sys
import os
from utility.game_state import <PERSON><PERSON><PERSON>
from utility.player import Player
from utility.monster import <PERSON><PERSON><PERSON><PERSON>
from utility.map_loader import MapLoader
from utility.image_handler import ImageHandler

class MarineEXE:
    def __init__(self):
        pygame.init()
        
        # Screen settings
        self.SCREEN_WIDTH = 1024
        self.SCREEN_HEIGHT = 768
        self.screen = pygame.display.set_mode((self.SCREEN_WIDTH, self.SCREEN_HEIGHT))
        pygame.display.set_caption("marine.EXE")
        
        # Game clock
        self.clock = pygame.time.Clock()
        self.FPS = 60
        
        # Game components
        self.game_state = GameState()
        self.image_handler = ImageHandler()
        self.map_loader = MapLoader()
        
        # Game objects
        self.player = None
        self.monster_manager = None
        
        # Colors
        self.BLACK = (0, 0, 0)
        self.WHITE = (255, 255, 255)
        self.GRAY = (128, 128, 128)
        self.GREEN = (0, 255, 0)
        self.RED = (255, 0, 0)
        
        # UI
        self.font = pygame.font.Font(None, 24)
        self.info_panel_rect = pygame.Rect(10, 10, 400, 150)
        
    def get_player_name(self):
        """Get player name input"""
        input_text = ""
        input_active = True
        
        while input_active:
            for event in pygame.event.get():
                if event.type == pygame.QUIT:
                    pygame.quit()
                    sys.exit()
                elif event.type == pygame.KEYDOWN:
                    if event.key == pygame.K_RETURN:
                        if input_text.strip():
                            return input_text.strip()
                    elif event.key == pygame.K_BACKSPACE:
                        input_text = input_text[:-1]
                    else:
                        input_text += event.unicode
            
            # Draw input screen
            self.screen.fill(self.BLACK)
            
            # Title
            title_text = self.font.render("marine.EXE", True, self.GREEN)
            title_rect = title_text.get_rect(center=(self.SCREEN_WIDTH//2, 200))
            self.screen.blit(title_text, title_rect)
            
            # Input prompt
            prompt_text = self.font.render("Enter your name:", True, self.WHITE)
            prompt_rect = prompt_text.get_rect(center=(self.SCREEN_WIDTH//2, 300))
            self.screen.blit(prompt_text, prompt_rect)
            
            # Input box
            input_box = pygame.Rect(self.SCREEN_WIDTH//2 - 150, 350, 300, 32)
            pygame.draw.rect(self.screen, self.WHITE, input_box, 2)
            
            # Input text
            text_surface = self.font.render(input_text, True, self.WHITE)
            self.screen.blit(text_surface, (input_box.x + 5, input_box.y + 5))
            
            # Instructions
            instr_text = self.font.render("Press ENTER to start", True, self.GRAY)
            instr_rect = instr_text.get_rect(center=(self.SCREEN_WIDTH//2, 450))
            self.screen.blit(instr_text, instr_rect)
            
            pygame.display.flip()
            self.clock.tick(self.FPS)
    
    def initialize_game(self, player_name):
        """Initialize game components"""
        # Load map
        map_data = self.map_loader.load_map(0)
        self.game_state.current_map = map_data
        self.game_state.player_name = player_name
        
        # Create player
        player_x = self.SCREEN_WIDTH // 2
        player_y = self.SCREEN_HEIGHT // 2
        self.player = Player(player_x, player_y, self.image_handler)
        
        # Create monster manager
        self.monster_manager = MonsterManager(
            self.SCREEN_WIDTH, 
            self.SCREEN_HEIGHT, 
            map_data, 
            self.image_handler
        )
    
    def draw_info_panel(self):
        """Draw the information panel"""
        # Panel background
        pygame.draw.rect(self.screen, self.BLACK, self.info_panel_rect)
        pygame.draw.rect(self.screen, self.WHITE, self.info_panel_rect, 2)
        
        # Player info
        y_offset = 20
        
        # Player name
        name_text = self.font.render(f"Player: {self.game_state.player_name}", True, self.WHITE)
        self.screen.blit(name_text, (20, y_offset))
        y_offset += 25
        
        # HP
        hp_text = self.font.render(f"HP: {self.player.hp}/{self.player.max_hp}", True, self.GREEN if self.player.hp > 30 else self.RED)
        self.screen.blit(hp_text, (20, y_offset))
        y_offset += 25
        
        # Level
        level_text = self.font.render(f"Level: {self.player.level}", True, self.WHITE)
        self.screen.blit(level_text, (20, y_offset))
        y_offset += 25
        
        # Map name
        map_name = self.game_state.current_map.get('map_name', 'Unknown')
        map_text = self.font.render(f"Map: {map_name}", True, self.WHITE)
        self.screen.blit(map_text, (20, y_offset))
        y_offset += 25
        
        # Monster count
        monster_count = len(self.monster_manager.monsters)
        monster_text = self.font.render(f"Monsters: {monster_count}", True, self.WHITE)
        self.screen.blit(monster_text, (20, y_offset))
    
    def run(self):
        """Main game loop"""
        # Get player name
        player_name = self.get_player_name()
        
        # Initialize game
        self.initialize_game(player_name)
        
        running = True
        while running:
            dt = self.clock.tick(self.FPS) / 1000.0  # Delta time in seconds
            
            # Handle events
            for event in pygame.event.get():
                if event.type == pygame.QUIT:
                    running = False
                elif event.type == pygame.MOUSEBUTTONDOWN:
                    if event.button == 1:  # Left mouse button
                        self.player.start_drag(pygame.mouse.get_pos())
                elif event.type == pygame.MOUSEBUTTONUP:
                    if event.button == 1:  # Left mouse button
                        self.player.stop_drag()
                elif event.type == pygame.MOUSEMOTION:
                    if self.player.dragging:
                        self.player.update_drag(pygame.mouse.get_pos())
            
            # Update game objects
            self.player.update(dt, self.monster_manager.monsters)
            self.monster_manager.update(dt, self.player)
            
            # Clear screen
            self.screen.fill(self.BLACK)
            
            # Draw game objects
            self.player.draw(self.screen)
            self.monster_manager.draw(self.screen)
            
            # Draw UI
            self.draw_info_panel()
            
            # Update display
            pygame.display.flip()
        
        pygame.quit()
        sys.exit()

if __name__ == "__main__":
    game = MarineEXE()
    game.run()
