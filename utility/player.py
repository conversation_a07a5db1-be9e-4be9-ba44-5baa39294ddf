import pygame
import math
from utility.projectile import ProjectileManager

class Player:
    def __init__(self, x, y, image_handler):
        self.x = x
        self.y = y
        self.image_handler = image_handler
        
        # Player stats
        self.max_hp = 100
        self.hp = self.max_hp
        self.damage = 10
        self.level = 1
        self.experience = 0
        
        # Load player image (keep original size)
        self.base_image = image_handler.load_image("res/player/marine.png")
        self.current_image = self.base_image

        # Get rect for collision and positioning
        self.rect = self.base_image.get_rect(center=(x, y))
        
        # Movement and animation
        self.angle = 0  # Current facing angle
        self.animation_time = 0.0
        self.moving = False
        
        # Dragging mechanics
        self.dragging = False
        self.drag_offset_x = 0
        self.drag_offset_y = 0
        
        # Shooting mechanics
        self.projectile_manager = ProjectileManager(image_handler)
        self.shoot_cooldown = 0.5  # Seconds between shots
        self.last_shot_time = 0.0
        self.weapon_offset = (55, 1)  # Weapon barrel position relative to player center

        # Movement
        self.speed = 100  # pixels per second
        self.velocity_x = 0
        self.velocity_y = 0
        
        # Target tracking
        self.current_target = None
        self.target_range = 300  # Maximum shooting range
        
    def start_drag(self, mouse_pos):
        """Start dragging the player"""
        # Check if mouse is over player
        if self.rect.collidepoint(mouse_pos):
            self.dragging = True
            self.drag_offset_x = mouse_pos[0] - self.x
            self.drag_offset_y = mouse_pos[1] - self.y
    
    def stop_drag(self):
        """Stop dragging the player"""
        self.dragging = False
    
    def update_drag(self, mouse_pos):
        """Update player position while dragging"""
        if self.dragging:
            new_x = mouse_pos[0] - self.drag_offset_x
            new_y = mouse_pos[1] - self.drag_offset_y
            
            # Update position
            self.x = new_x
            self.y = new_y
            self.rect.center = (self.x, self.y)
            self.moving = True
    
    def find_nearest_enemy(self, enemies):
        """Find the nearest enemy within range"""
        if not enemies:
            return None
        
        nearest_enemy = None
        nearest_distance = float('inf')
        
        for enemy in enemies:
            if hasattr(enemy, 'x') and hasattr(enemy, 'y'):
                distance = self.image_handler.get_distance((self.x, self.y), (enemy.x, enemy.y))
                
                if distance <= self.target_range and distance < nearest_distance:
                    nearest_distance = distance
                    nearest_enemy = enemy
        
        return nearest_enemy
    
    def calculate_rotated_weapon_position(self):
        """Calculate weapon position after rotation"""
        # Convert angle to radians
        angle_rad = math.radians(self.angle)

        # Rotate the weapon offset
        cos_a = math.cos(angle_rad)
        sin_a = math.sin(angle_rad)

        rotated_x = self.weapon_offset[0] * cos_a - self.weapon_offset[1] * sin_a
        rotated_y = self.weapon_offset[0] * sin_a + self.weapon_offset[1] * cos_a

        return (self.x + rotated_x, self.y + rotated_y)

    def shoot_at_target(self, target, current_time):
        """Shoot at the specified target"""
        if current_time - self.last_shot_time >= self.shoot_cooldown:
            # Calculate rotated weapon position
            spawn_x, spawn_y = self.calculate_rotated_weapon_position()

            # Shoot at target
            self.projectile_manager.add_projectile(
                spawn_x, spawn_y,
                target.x, target.y,
                self.damage
            )

            self.last_shot_time = current_time
    
    def update_rotation(self, target):
        """Update player rotation to face target"""
        if target:
            self.angle = self.image_handler.calculate_angle_to_target(
                (self.x, self.y), 
                (target.x, target.y)
            )
    
    def update_animation(self, dt):
        """Update movement animation"""
        self.animation_time += dt
        
        if self.moving:
            # Create walking animation
            self.current_image = self.image_handler.get_animated_frame(
                self.base_image, 
                self.animation_time,
                animation_speed=4.0,
                tilt_amount=3.0
            )
        else:
            self.current_image = self.base_image
        
        # Apply rotation
        if self.angle != 0:
            self.current_image = self.image_handler.rotate_image(self.current_image, self.angle)
        
        # Update rect
        self.rect = self.current_image.get_rect(center=(self.x, self.y))
        
        # Reset moving flag
        self.moving = False
    
    def take_damage(self, damage):
        """Take damage and return True if player dies"""
        self.hp -= damage
        if self.hp <= 0:
            self.hp = 0
            return True
        return False
    
    def heal(self, amount):
        """Heal the player"""
        self.hp = min(self.max_hp, self.hp + amount)
    
    def handle_movement(self, dt):
        """Handle keyboard movement"""
        keys = pygame.key.get_pressed()

        self.velocity_x = 0
        self.velocity_y = 0

        if keys[pygame.K_w] or keys[pygame.K_UP]:
            self.velocity_y = -self.speed
        if keys[pygame.K_s] or keys[pygame.K_DOWN]:
            self.velocity_y = self.speed
        if keys[pygame.K_a] or keys[pygame.K_LEFT]:
            self.velocity_x = -self.speed
        if keys[pygame.K_d] or keys[pygame.K_RIGHT]:
            self.velocity_x = self.speed

        # Apply movement
        if self.velocity_x != 0 or self.velocity_y != 0:
            self.x += self.velocity_x * dt
            self.y += self.velocity_y * dt
            self.rect.center = (self.x, self.y)
            self.moving = True

    def update(self, dt, enemies):
        """Update player state"""
        current_time = pygame.time.get_ticks() / 1000.0

        # Handle movement (only if not being dragged)
        if not self.dragging:
            self.handle_movement(dt)

        # Find and shoot at nearest enemy
        self.current_target = self.find_nearest_enemy(enemies)

        if self.current_target:
            self.update_rotation(self.current_target)
            self.shoot_at_target(self.current_target, current_time)

        # Update animation
        self.update_animation(dt)

        # Update projectiles
        screen_width = pygame.display.get_surface().get_width()
        screen_height = pygame.display.get_surface().get_height()
        self.projectile_manager.update(dt, screen_width, screen_height)

        # Check projectile collisions with enemies
        hits = self.projectile_manager.check_collisions(enemies)
        for projectile, enemy in hits:
            if hasattr(enemy, 'take_damage'):
                enemy.take_damage(projectile.damage)
    
    def draw(self, screen):
        """Draw the player and projectiles"""
        # Draw player
        screen.blit(self.current_image, self.rect)
        
        # Draw health bar above player
        health_bar = self.image_handler.create_health_bar(40, 6, self.hp, self.max_hp)
        health_bar_rect = health_bar.get_rect(center=(self.x, self.y - 30))
        screen.blit(health_bar, health_bar_rect)
        
        # Draw projectiles
        self.projectile_manager.draw(screen)
        
        # Draw targeting line (debug) - commented out for desktop overlay
        # if self.current_target:
        #     pygame.draw.line(screen, (255, 255, 0), 
        #                    (self.x, self.y), 
        #                    (self.current_target.x, self.current_target.y), 1)
