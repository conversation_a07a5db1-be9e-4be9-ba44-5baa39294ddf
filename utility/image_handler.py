import pygame
import math
import os

class ImageHandler:
    def __init__(self):
        self.image_cache = {}
        self.rotated_cache = {}
    
    def load_image(self, path, scale_factor=0.5):
        """Load and cache an image with transparency support"""
        cache_key = f"{path}_{scale_factor}"
        if cache_key in self.image_cache:
            return self.image_cache[cache_key]

        try:
            # Load image with alpha channel for transparency
            image = pygame.image.load(path).convert_alpha()

            # Scale down the image to half size
            if scale_factor != 1.0:
                new_width = int(image.get_width() * scale_factor)
                new_height = int(image.get_height() * scale_factor)
                image = pygame.transform.scale(image, (new_width, new_height))

            self.image_cache[cache_key] = image
            return image
        except pygame.error as e:
            print(f"Could not load image {path}: {e}")
            # Return a placeholder image
            placeholder = pygame.Surface((16, 16), pygame.SRCALPHA)  # Smaller placeholder
            placeholder.fill((255, 0, 255, 128))  # Magenta placeholder
            return placeholder
    
    def rotate_image(self, image, angle):
        """Rotate an image while maintaining transparency"""
        cache_key = (id(image), angle)
        if cache_key in self.rotated_cache:
            return self.rotated_cache[cache_key]
        
        # Rotate the image
        rotated = pygame.transform.rotate(image, angle)
        self.rotated_cache[cache_key] = rotated
        return rotated
    
    def get_rotated_image(self, path, angle):
        """Get a rotated version of an image"""
        original = self.load_image(path)
        return self.rotate_image(original, angle)
    
    def calculate_angle_to_target(self, from_pos, to_pos):
        """Calculate angle from one position to another"""
        dx = to_pos[0] - from_pos[0]
        dy = to_pos[1] - from_pos[1]
        
        # Calculate angle in degrees (0 degrees = pointing right)
        angle = math.degrees(math.atan2(-dy, dx))
        
        # Convert to pygame rotation (0 degrees = pointing up)
        # Since our sprites face south by default, we need to adjust
        angle = angle - 90
        
        return angle
    
    def get_animated_frame(self, base_image, time, animation_speed=2.0, tilt_amount=5.0):
        """Create a simple walking animation by tilting the sprite"""
        # Calculate tilt based on time
        tilt = math.sin(time * animation_speed) * tilt_amount
        
        # Apply tilt
        if abs(tilt) > 0.1:
            return self.rotate_image(base_image, tilt)
        else:
            return base_image
    
    def scale_image(self, image, scale_factor):
        """Scale an image while maintaining transparency"""
        if scale_factor == 1.0:
            return image
        
        new_width = int(image.get_width() * scale_factor)
        new_height = int(image.get_height() * scale_factor)
        
        return pygame.transform.scale(image, (new_width, new_height))
    
    def get_image_center(self, image):
        """Get the center point of an image"""
        rect = image.get_rect()
        return rect.center
    
    def create_health_bar(self, width, height, current_hp, max_hp):
        """Create a health bar surface"""
        surface = pygame.Surface((width, height), pygame.SRCALPHA)
        
        # Background
        pygame.draw.rect(surface, (64, 64, 64), (0, 0, width, height))
        
        # Health bar
        if max_hp > 0:
            health_width = int((current_hp / max_hp) * (width - 2))
            if health_width > 0:
                # Color based on health percentage
                health_percent = current_hp / max_hp
                if health_percent > 0.6:
                    color = (0, 255, 0)  # Green
                elif health_percent > 0.3:
                    color = (255, 255, 0)  # Yellow
                else:
                    color = (255, 0, 0)  # Red
                
                pygame.draw.rect(surface, color, (1, 1, health_width, height - 2))
        
        # Border
        pygame.draw.rect(surface, (255, 255, 255), (0, 0, width, height), 1)
        
        return surface
    
    def get_distance(self, pos1, pos2):
        """Calculate distance between two positions"""
        dx = pos2[0] - pos1[0]
        dy = pos2[1] - pos1[1]
        return math.sqrt(dx * dx + dy * dy)
    
    def normalize_vector(self, vector):
        """Normalize a 2D vector"""
        length = math.sqrt(vector[0] ** 2 + vector[1] ** 2)
        if length == 0:
            return (0, 0)
        return (vector[0] / length, vector[1] / length)
    
    def move_towards(self, current_pos, target_pos, speed, dt):
        """Move from current position towards target at given speed"""
        dx = target_pos[0] - current_pos[0]
        dy = target_pos[1] - current_pos[1]
        distance = math.sqrt(dx * dx + dy * dy)
        
        if distance <= speed * dt:
            return target_pos
        
        # Normalize and apply speed
        if distance > 0:
            dx = (dx / distance) * speed * dt
            dy = (dy / distance) * speed * dt
            return (current_pos[0] + dx, current_pos[1] + dy)
        
        return current_pos
    
    def is_point_in_circle(self, point, circle_center, radius):
        """Check if a point is inside a circle"""
        distance = self.get_distance(point, circle_center)
        return distance <= radius
    
    def clamp(self, value, min_val, max_val):
        """Clamp a value between min and max"""
        return max(min_val, min(value, max_val))
