import pygame
import random
import math
import time

class Monster:
    def __init__(self, x, y, monster_type, image_handler, level=1):
        self.x = x
        self.y = y
        self.monster_type = monster_type
        self.image_handler = image_handler
        self.level = level
        
        # Monster stats (will be customized later)
        self.max_hp = 20
        self.hp = self.max_hp
        self.damage = 2
        self.speed = 50  # pixels per second
        self.attack_range = 30
        self.attack_cooldown = 1.0  # seconds
        self.last_attack_time = 0.0
        
        # Load monster image
        image_path = f"res/monster/{monster_type}/monster.png"
        self.base_image = image_handler.load_image(image_path)
        self.current_image = self.base_image
        
        # Get rect for collision
        self.rect = self.base_image.get_rect(center=(x, y))
        
        # Movement and animation
        self.angle = 0
        self.animation_time = 0.0
        self.moving = False
        
        # AI state
        self.target = None
        self.state = "seeking"  # seeking, attacking, dead
        
        # Death animation
        self.death_time = 0.0
        self.death_duration = 0.5
        
    def update(self, dt, player):
        """Update monster behavior"""
        if self.state == "dead":
            self.death_time += dt
            return
        
        if self.hp <= 0:
            self.state = "dead"
            return
        
        self.animation_time += dt
        self.target = player
        
        # Move towards player
        if self.target:
            distance = self.image_handler.get_distance((self.x, self.y), (self.target.x, self.target.y))
            
            if distance > self.attack_range:
                # Move towards player
                new_pos = self.image_handler.move_towards(
                    (self.x, self.y),
                    (self.target.x, self.target.y),
                    self.speed,
                    dt
                )
                self.x, self.y = new_pos
                self.moving = True
                
                # Update rotation to face player
                self.angle = self.image_handler.calculate_angle_to_target(
                    (self.x, self.y),
                    (self.target.x, self.target.y)
                )
            else:
                # Attack player
                self.attack_player(player)
                self.moving = False
        
        # Update animation
        self.update_animation(dt)
        
        # Update rect position
        self.rect.center = (self.x, self.y)
    
    def attack_player(self, player):
        """Attack the player if in range and cooldown is ready"""
        current_time = time.time()
        
        if current_time - self.last_attack_time >= self.attack_cooldown:
            # Deal damage to player
            if hasattr(player, 'take_damage'):
                player.take_damage(self.damage)
            
            self.last_attack_time = current_time
            print(f"{self.monster_type} attacks for {self.damage} damage!")
    
    def update_animation(self, dt):
        """Update monster animation"""
        if self.state == "dead":
            # Death animation - fade out
            alpha = max(0, 255 - int((self.death_time / self.death_duration) * 255))
            self.current_image = self.base_image.copy()
            self.current_image.set_alpha(alpha)
            return
        
        if self.moving:
            # Walking animation
            self.current_image = self.image_handler.get_animated_frame(
                self.base_image,
                self.animation_time,
                animation_speed=3.0,
                tilt_amount=4.0
            )
        else:
            self.current_image = self.base_image
        
        # Apply rotation
        if self.angle != 0:
            self.current_image = self.image_handler.rotate_image(self.current_image, self.angle)
        
        # Reset moving flag
        self.moving = False
    
    def take_damage(self, damage):
        """Take damage and return True if monster dies"""
        if self.state == "dead":
            return False
        
        self.hp -= damage
        if self.hp <= 0:
            self.hp = 0
            self.state = "dead"
            return True
        return False
    
    def draw(self, screen):
        """Draw the monster"""
        if self.state == "dead" and self.death_time >= self.death_duration:
            return  # Don't draw if death animation is complete
        
        # Draw monster
        screen.blit(self.current_image, self.rect)
        
        # Draw health bar if damaged
        if self.hp < self.max_hp and self.state != "dead":
            health_bar = self.image_handler.create_health_bar(30, 4, self.hp, self.max_hp)
            health_bar_rect = health_bar.get_rect(center=(self.x, self.y - 25))
            screen.blit(health_bar, health_bar_rect)
    
    def is_dead_and_finished(self):
        """Check if monster is dead and death animation is complete"""
        return self.state == "dead" and self.death_time >= self.death_duration

class MonsterManager:
    def __init__(self, screen_width, screen_height, map_data, image_handler):
        self.screen_width = screen_width
        self.screen_height = screen_height
        self.map_data = map_data
        self.image_handler = image_handler
        
        self.monsters = []
        self.spawn_timer = 0.0
        self.spawn_cooldown = 10.0  # Default spawn rate
        
        # Parse spawn rate from map data
        if 'monster_spawn_rate_min' in map_data and 'monster_spawn_rate_max' in map_data:
            min_rate = map_data['monster_spawn_rate_min']
            max_rate = map_data['monster_spawn_rate_max']
            self.spawn_cooldown = random.uniform(min_rate, max_rate)
        
        self.max_monsters = map_data.get('maximum_monsters', 12)
        self.monster_types = map_data.get('monster_types', {})
        
        # Create weighted list for monster spawning
        self.weighted_monster_list = []
        for monster_type, weight in self.monster_types.items():
            self.weighted_monster_list.extend([monster_type] * weight)
    
    def get_spawn_position(self):
        """Get a random spawn position at the edge of the screen"""
        edge = random.randint(0, 3)  # 0=top, 1=right, 2=bottom, 3=left
        
        if edge == 0:  # Top
            return (random.randint(0, self.screen_width), -50)
        elif edge == 1:  # Right
            return (self.screen_width + 50, random.randint(0, self.screen_height))
        elif edge == 2:  # Bottom
            return (random.randint(0, self.screen_width), self.screen_height + 50)
        else:  # Left
            return (-50, random.randint(0, self.screen_height))
    
    def spawn_monster(self):
        """Spawn a new monster"""
        if len(self.monsters) >= self.max_monsters:
            return
        
        if not self.weighted_monster_list:
            return
        
        # Choose random monster type based on weights
        monster_type = random.choice(self.weighted_monster_list)
        
        # Get spawn position
        x, y = self.get_spawn_position()
        
        # Create monster
        level = random.randint(
            self.map_data.get('monster_min_level', 1),
            self.map_data.get('monster_max_level', 5)
        )
        
        monster = Monster(x, y, monster_type, self.image_handler, level)
        self.monsters.append(monster)
        
        print(f"Spawned {monster_type} at ({x}, {y})")
    
    def update(self, dt, player):
        """Update all monsters"""
        # Update spawn timer
        self.spawn_timer += dt
        
        if self.spawn_timer >= self.spawn_cooldown:
            self.spawn_monster()
            self.spawn_timer = 0.0
            
            # Randomize next spawn time
            if 'monster_spawn_rate_min' in self.map_data and 'monster_spawn_rate_max' in self.map_data:
                min_rate = self.map_data['monster_spawn_rate_min']
                max_rate = self.map_data['monster_spawn_rate_max']
                self.spawn_cooldown = random.uniform(min_rate, max_rate)
        
        # Update existing monsters
        for monster in self.monsters:
            monster.update(dt, player)
        
        # Remove dead monsters that finished their death animation
        self.monsters = [m for m in self.monsters if not m.is_dead_and_finished()]
    
    def draw(self, screen):
        """Draw all monsters"""
        for monster in self.monsters:
            monster.draw(screen)
    
    def get_living_monsters(self):
        """Get list of living monsters"""
        return [m for m in self.monsters if m.state != "dead"]
    
    def clear_all(self):
        """Clear all monsters"""
        self.monsters.clear()
